<template>
  <div class="dialog-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="false"
      :filter="true"
      :menuConfig="menuConfig"
      :checkAll="false"
      :checkStrictly="true"
      @cell-dblclick="cellDbClickHandler"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, useTemplateRef } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { DeviceTypes, getDeviceMapLonLat } from '@/utils/bfutil'
  import { MenuConfig, TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import { VxeTableEvents } from 'vxe-table'
  import { mapFlyTo } from '@/utils/map'
  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]

  const statusContextMenuCode = ['stats', 'cb01', 'cb02', 'cb09']

  const { t } = useI18n()

  const tableTreeRef = useTemplateRef('tableTree')

  const menuConfig = computed<MenuConfig>(() => {
    return {
      body: {
        options: [
          [
            {
              name: t('tree.quickCall'),
              code: 'quickCall',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.locateCtrl'),
              code: 'cb01',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.trailCtrl'),
              code: 'cb02',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.telecontrol'),
              code: 'cb09',
            },
            {
              disabled: true,
              visible: false,
              name: t('tree.status'),
              code: 'stats',
            },
          ],
          [
            {
              name: t('tree.collapseAll'),
              code: 'collapseAll',
            },
            {
              name: t('tree.expandAll'),
              code: 'expandAll',
            },
            {
              name: t('tree.online'),
              code: 'displayOnline',
            },
            {
              name: t('tree.displayAllDev'),
              code: 'displayAll',
            },
          ],
        ],
      },
      visibleMethod: ({ options, row }) => {
        if (!row) return true
        let hasStatus = false

        if (row.nodeType === TreeNodeType.Terminal) {
          const device = bfglob.gdevices.get(row.rid)
          // 只有指定的设备才显示状态
          if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
            hasStatus = true
          }
        }

        const visible = row.nodeType === TreeNodeType.Org ? false : hasStatus
        const disabled = !visible

        options?.forEach(list => {
          list.forEach(item => {
            if (statusContextMenuCode.includes(item.code)) {
              item.visible = visible
              item.disabled = disabled
            }
          })
        })

        return true
      },
    } satisfies MenuConfig
  })

  const cellDbClickHandler: VxeTableEvents.CellDblclick<TreeNodeData> = ({ row }) => {
    // 处理单元格双击事件
    if (row.nodeType === TreeNodeType.Org) {
      const org = bfglob.gorgData.get(row.rid)
      if (!org) {
        return
      }
      // use relative org marker lonlat
      const lonlatInfo = bfglob.gorgData.getOrgMapMakerPointLonlatInfo(org.rid)
      if (!lonlatInfo.lonlat) {
        return
      }
      mapFlyTo(lonlatInfo.lonlat, lonlatInfo.showLevel)
    } else {
      const device = bfglob.gdevices.get(row.rid)
      if (!device) {
        return
      }
      mapFlyTo(getDeviceMapLonLat(device))
    }
  }
</script>

<style lang="scss">
  .dispatch-tree-wrapper {
    height: 100%;
    width: 100%;
  }
</style>
