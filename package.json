{"name": "bfdx8000-system", "description": "A Vue.js project", "version": "3.6.10", "minServerVersion": "2.93.5", "repository": "*******************:bfdx/bf8100-web.git", "author": "linfulong <<EMAIL>>", "license": "MIT", "private": true, "type": "module", "scripts": {"serve": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --fix src", "pbjs": "./pbjs.sh", "wf": "cd doc/wf && markdown-pdf BF8100-WF-infomation.md -o ../../build/BF8100-手台写频操作说明.pdf", "gateway": "cd doc/gateway && markdown-pdf gateway-operate-info.md -o ../../build/电话网关频操作说明.pdf", "build:md": "yarn wf && yarn run gateway", "model": "node ./generateModelInfo.cjs"}, "dependencies": {"@bufbuild/protobuf": "^1.6.0", "@connectrpc/connect": "^1.2.0", "@connectrpc/connect-web": "^1.2.0", "@element-plus/icons-vue": "^2.3.1", "@mdi/font": "^7.2.96", "aes-js": "^3.1.2", "async-validator": "^4.2.5", "base64-js": "^1.5.1", "bootstrap": "^4.6.0", "compare-versions": "^6.1.1", "crypto-js": "^4.0.0", "datatables.net-bs4": "^2.2.2", "datatables.net-buttons-bs4": "^3.2.2", "datatables.net-scroller-bs4": "^2.4.3", "datatables.net-staterestore-bs4": "^1.4.1", "datatables.net-vue3": "^2.1.0", "dayjs": "^1.10.3", "detect-browser": "^5.2.0", "element-plus": "^2.5.6", "jquery": "^3.6.0", "jquery-ui": "~1.13.0", "jquery.fancytree": "^2.37.0", "lodash": "^4.17.20", "long": "^4.0.0", "maplibre-gl": "^5.5.0", "pako": "^1.0.11", "protobufjs": "^6.10.2", "qwebchannel": "~5.9.0", "screenfull": "^5.1.0", "turf": "^3.0.14", "ui-contextmenu": "^1.18.1", "uuid": "^8.3.2", "vue": "^3.4.21", "vue-i18n": "^9.0.0", "vue-router": "^4.2.5", "vue-virtual-scroller": "^2.0.0-beta.8", "websocket-nats": "^0.3.3", "xlsx": "^0.18.5", "ypubsub": "^1.0.15"}, "devDependencies": {"@bufbuild/buf": "^1.27.2", "@bufbuild/protoc-gen-es": "^1.6.0", "@connectrpc/protoc-gen-connect-es": "^1.2.0", "@eslint/js": "^9.20.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.5", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@tailwindcss/vite": "^4.0.6", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-standard": "^9.0.0", "consola": "^3.4.0", "conventional-changelog-conventionalcommits": "^9.0.0", "eslint": "^9.20.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^9.32.0", "globals": "^15.15.0", "sass": "^1.85.0", "semantic-release": "^24.2.5", "setimmediate": "^1.0.5", "tailwindcss": "^4.0.6", "terser": "^5.38.1", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^5.0.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-node-polyfills": "^0.23.0", "vue-template-compiler": "^2.7.16"}}